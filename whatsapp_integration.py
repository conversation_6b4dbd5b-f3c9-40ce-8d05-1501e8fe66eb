"""
WhatsApp Integration Module with Payment Verification

This module handles the integration between the WhatsApp API and the payment system,
ensuring that only verified and paid users can access the AI agent features.
"""

import os
import sys
import json
import time
from typing import List, Dict, Any, Union, Tuple
import requests
from datetime import datetime

# Import payment system
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from payment_system.payment_system import PaymentSystem

class WhatsAppIntegration:
    """Class for integrating WhatsApp with the payment system."""
    
    def __init__(self, payment_system: PaymentSystem, whatsapp_api_url: str, whatsapp_api_key: str):
        """
        Initialize the WhatsApp integration.
        
        Args:
            payment_system: Instance of the PaymentSystem class
            whatsapp_api_url: URL for the WhatsApp Business API
            whatsapp_api_key: API key for the WhatsApp Business API
        """
        self.payment_system = payment_system
        self.whatsapp_api_url = whatsapp_api_url
        self.whatsapp_api_key = whatsapp_api_key
        
    def verify_user_access(self, phone_number: str) -> Dict[str, Any]:
        """
        Verify if a user has access to the AI agent.
        
        Args:
            phone_number: User's WhatsApp phone number
            
        Returns:
            Dictionary with verification result
        """
        # Check if the phone number is verified in the payment system
        verification_result = self.payment_system.check_whatsapp_verification(phone_number)
        
        if not verification_result['success']:
            return {
                'has_access': False,
                'message': 'Phone number not registered',
                'verification_status': 'not_registered'
            }
        
        if not verification_result['is_verified']:
            return {
                'has_access': False,
                'message': 'Phone number not verified',
                'verification_status': 'not_verified'
            }
        
        # Check if the user has sufficient credits
        if verification_result['credit_balance'] <= 0:
            return {
                'has_access': False,
                'message': 'Insufficient credits',
                'verification_status': 'verified',
                'user_id': verification_result['user_id'],
                'username': verification_result['username'],
                'credit_balance': verification_result['credit_balance']
            }
        
        return {
            'has_access': True,
            'message': 'Access granted',
            'verification_status': 'verified',
            'user_id': verification_result['user_id'],
            'username': verification_result['username'],
            'credit_balance': verification_result['credit_balance']
        }
    
    def process_incoming_message(self, message_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Process an incoming WhatsApp message.
        
        Args:
            message_data: Dictionary containing message data
            
        Returns:
            Dictionary with processing result
        """
        try:
            # Extract phone number and message content
            phone_number = message_data.get('from', '')
            message_content = message_data.get('text', {}).get('body', '')
            
            # Check if this is a verification code
            if message_content.isdigit() and len(message_content) == 6:
                # Attempt to verify the WhatsApp number
                verification_result = self.payment_system.verify_whatsapp(phone_number, message_content)
                
                if verification_result['success']:
                    # Send confirmation message
                    self.send_message(
                        phone_number,
                        "Your WhatsApp number has been successfully verified. You can now use the AI agent."
                    )
                    return {
                        'success': True,
                        'action': 'verification',
                        'result': verification_result
                    }
                else:
                    # Send error message
                    self.send_message(
                        phone_number,
                        f"Verification failed: {verification_result['message']}"
                    )
                    return {
                        'success': False,
                        'action': 'verification',
                        'result': verification_result
                    }
            
            # Check if user has access
            access_result = self.verify_user_access(phone_number)
            
            if not access_result['has_access']:
                # Send access denied message
                if access_result['verification_status'] == 'not_registered':
                    self.send_message(
                        phone_number,
                        "Your WhatsApp number is not registered. Please visit our website to create an account and register your number."
                    )
                elif access_result['verification_status'] == 'not_verified':
                    self.send_message(
                        phone_number,
                        "Your WhatsApp number is not verified. Please check your verification code and send it to this number."
                    )
                else:  # Insufficient credits
                    self.send_message(
                        phone_number,
                        f"You have insufficient credits to use the AI agent. Your current balance is {access_result['credit_balance']} credits. Please visit our website to purchase more credits."
                    )
                
                return {
                    'success': False,
                    'action': 'access_check',
                    'result': access_result
                }
            
            # Process the message based on its type
            if 'type' in message_data:
                message_type = message_data['type']
                
                if message_type == 'text':
                    # Process text message
                    return self._process_text_message(phone_number, message_content, access_result['user_id'])
                elif message_type == 'image':
                    # Process image message
                    image_url = message_data.get('image', {}).get('url', '')
                    return self._process_image_message(phone_number, image_url, access_result['user_id'])
                elif message_type == 'document':
                    # Process document message
                    document_url = message_data.get('document', {}).get('url', '')
                    document_name = message_data.get('document', {}).get('filename', '')
                    return self._process_document_message(phone_number, document_url, document_name, access_result['user_id'])
                else:
                    # Unsupported message type
                    self.send_message(
                        phone_number,
                        f"Sorry, message type '{message_type}' is not supported. Please send text, images, or documents."
                    )
                    return {
                        'success': False,
                        'action': 'message_processing',
                        'error': 'Unsupported message type'
                    }
            else:
                # Missing message type
                self.send_message(
                    phone_number,
                    "Sorry, there was an error processing your message. Please try again."
                )
                return {
                    'success': False,
                    'action': 'message_processing',
                    'error': 'Missing message type'
                }
        
        except Exception as e:
            # Log the error
            print(f"Error processing incoming message: {str(e)}")
            
            # Try to send error message if phone number is available
            if 'phone_number' in locals():
                self.send_message(
                    phone_number,
                    "Sorry, there was an error processing your message. Please try again later."
                )
            
            return {
                'success': False,
                'action': 'message_processing',
                'error': str(e)
            }
    
    def _process_text_message(self, phone_number: str, message_content: str, user_id: str) -> Dict[str, Any]:
        """
        Process a text message.
        
        Args:
            phone_number: User's WhatsApp phone number
            message_content: Text message content
            user_id: User's ID
            
        Returns:
            Dictionary with processing result
        """
        # In a real implementation, this would call the text processing module
        # Here we just send a placeholder response
        
        # Calculate credits to use (example: 1 credit per text message)
        credits_to_use = 1.0
        
        # Use credits
        usage_result = self.payment_system.use_credits(
            user_id=user_id,
            amount=credits_to_use,
            service_type='text_question',
            details=f"Text question: {message_content[:50]}..."
        )
        
        if not usage_result['success']:
            # Send error message
            self.send_message(
                phone_number,
                f"Error: {usage_result['message']}"
            )
            return {
                'success': False,
                'action': 'text_processing',
                'result': usage_result
            }
        
        # Send acknowledgment message
        self.send_message(
            phone_number,
            f"Processing your question: '{message_content}'. This will use {credits_to_use} credits. Your new balance is {usage_result['new_balance']} credits."
        )
        
        # In a real implementation, this would process the text and generate an answer
        # For now, we just send a placeholder response
        time.sleep(2)  # Simulate processing time
        
        answer = f"This is a placeholder answer to your question: '{message_content}'"
        
        # Send the answer
        self.send_message(phone_number, answer)
        
        return {
            'success': True,
            'action': 'text_processing',
            'credits_used': credits_to_use,
            'new_balance': usage_result['new_balance']
        }
    
    def _process_image_message(self, phone_number: str, image_url: str, user_id: str) -> Dict[str, Any]:
        """
        Process an image message.
        
        Args:
            phone_number: User's WhatsApp phone number
            image_url: URL of the image
            user_id: User's ID
            
        Returns:
            Dictionary with processing result
        """
        # In a real implementation, this would download the image and call the image processing module
        # Here we just send a placeholder response
        
        # Calculate credits to use (example: 2 credits per image)
        credits_to_use = 2.0
        
        # Use credits
        usage_result = self.payment_system.use_credits(
            user_id=user_id,
            amount=credits_to_use,
            service_type='image_question',
            details=f"Image question from URL: {image_url}"
        )
        
        if not usage_result['success']:
            # Send error message
            self.send_message(
                phone_number,
                f"Error: {usage_result['message']}"
            )
            return {
                'success': False,
                'action': 'image_processing',
                'result': usage_result
            }
        
        # Send acknowledgment message
        self.send_message(
            phone_number,
            f"Processing your image. This will use {credits_to_use} credits. Your new balance is {usage_result['new_balance']} credits."
        )
        
        # In a real implementation, this would process the image and generate an answer
        # For now, we just send a placeholder response
        time.sleep(3)  # Simulate processing time
        
        answer = "This is a placeholder answer based on the image you sent."
        
        # Send the answer
        self.send_message(phone_number, answer)
        
        return {
            'success': True,
            'action': 'image_processing',
            'credits_used': credits_to_use,
            'new_balance': usage_result['new_balance']
        }
    
    def _process_document_message(self, phone_number: str, document_url: str, document_name: str, user_id: str) -> Dict[str, Any]:
        """
        Process a document message.
        
        Args:
            phone_number: User's WhatsApp phone number
            document_url: URL of the document
            document_name: Name of the document
            user_id: User's ID
            
        Returns:
            Dictionary with processing result
        """
        # In a real implementation, this would download the document and call the document processing module
        # Here we just send a placeholder response
        
        # Check if this is a question paper (based on filename)
        is_question_paper = 'question' in document_name.lower() or 'exam' in document_name.lower() or 'test' in document_name.lower()
        
        # Calculate credits to use (example: 5 credits for regular document, 10 for question paper)
        credits_to_use = 10.0 if is_question_paper else 5.0
        
        # Use credits
        usage_result = self.payment_system.use_credits(
            user_id=user_id,
            amount=credits_to_use,
            service_type='document_processing',
            details=f"Document processing: {document_name} (Question paper: {is_question_paper})"
        )
        
        if not usage_result['success']:
            # Send error message
            self.send_message(
                phone_number,
                f"Error: {usage_result['message']}"
            )
            return {
                'success': False,
                'action': 'document_processing',
                'result': usage_result
            }
        
        # Send acknowledgment message
        doc_type = "question paper" if is_question_paper else "document"
        self.send_message(
            phone_number,
            f"Processing your {doc_type}: {document_name}. This will use {credits_to_use} credits. Your new balance is {usage_result['new_balance']} credits."
        )
        
        # In a real implementation, this would process the document and generate answers
        # For now, we just send a placeholder response
        time.sleep(5)  # Simulate processing time
        
        if is_question_paper:
            answer = f"I've analyzed the question paper '{document_name}' and prepared answers for all questions. Would you like the answers as text messages or as a PDF document? Requesting a PDF will use an additional 2 credits."
        else:
            answer = f"I've analyzed the document '{document_name}' and extracted the following information: [Placeholder for document analysis]"
        
        # Send the answer
        self.send_message(phone_number, answer)
        
        return {
            'success': True,
            'action': 'document_processing',
            'document_type': 'question_paper' if is_question_paper else 'regular',
            'credits_used': credits_to_use,
            'new_balance': usage_result['new_balance']
        }
    
    def generate_and_send_pdf(self, phone_number: str, user_id: str, content_type: s
(Content truncated due to size limit. Use line ranges to read in chunks)