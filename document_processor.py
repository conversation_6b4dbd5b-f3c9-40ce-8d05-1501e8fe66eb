"""
Document Processing Module for WhatsApp AI Agent

This module handles the processing of document-based questions received through WhatsApp.
It extracts text from various document formats (PDF, DOCX, etc.), identifies questions,
and prepares them for the answer generation module. It also includes specialized
functionality for processing entire question papers.
"""

import os
import re
import fitz  # PyMuPDF
import docx
import csv
from typing import List, Dict, Any, Union, Tuple
import nltk
from nltk.tokenize import sent_tokenize

# Import the text processor to reuse question extraction logic
import sys
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from text_processing.text_processor import TextProcessor

class DocumentProcessor:
    """Class for processing document-based questions from WhatsApp messages."""
    
    def __init__(self):
        """Initialize the document processor."""
        self.text_processor = TextProcessor()
        self.supported_extensions = {
            '.pdf': self._extract_text_from_pdf,
            '.docx': self._extract_text_from_docx,
            '.doc': self._extract_text_from_docx,  # May not work perfectly for .doc
            '.txt': self._extract_text_from_txt,
            '.csv': self._extract_text_from_csv
        }
        
    def process_document(self, document_path: str) -> List[Dict[str, Any]]:
        """
        Process a document to extract text and questions.
        
        Args:
            document_path: Path to the document file
            
        Returns:
            List of processed questions with analysis
        """
        # Check if file exists
        if not os.path.exists(document_path):
            print(f"Error: Document file {document_path} not found.")
            return []
        
        # Get file extension
        _, ext = os.path.splitext(document_path)
        ext = ext.lower()
        
        # Check if file type is supported
        if ext not in self.supported_extensions:
            print(f"Error: Unsupported document format {ext}.")
            return []
        
        # Extract text from document
        extracted_text = self.supported_extensions[ext](document_path)
        
        # If no text was extracted, return empty list
        if not extracted_text:
            return []
        
        # Use text processor to extract and analyze questions
        processed_questions = self.text_processor.process_text_input(extracted_text)
        
        # Add source information
        for question in processed_questions:
            question['source'] = {
                'type': 'document',
                'path': document_path,
                'format': ext[1:],  # Remove the dot from extension
            }
        
        return processed_questions
    
    def process_question_paper(self, document_path: str) -> List[Dict[str, Any]]:
        """
        Process a question paper to extract all questions with structure.
        This method is specialized for handling educational question papers.
        
        Args:
            document_path: Path to the question paper document
            
        Returns:
            List of processed questions with analysis and structure information
        """
        # Extract text from document
        _, ext = os.path.splitext(document_path)
        ext = ext.lower()
        
        if ext not in self.supported_extensions:
            print(f"Error: Unsupported document format {ext}.")
            return []
        
        extracted_text = self.supported_extensions[ext](document_path)
        
        # If no text was extracted, return empty list
        if not extracted_text:
            return []
        
        # Identify question paper structure
        structured_questions = self._identify_question_paper_structure(extracted_text)
        
        # Process each question
        processed_questions = []
        for question_info in structured_questions:
            # Use text processor to analyze the question
            analysis = self.text_processor.analyze_question(question_info['text'])
            
            # Combine analysis with structure information
            processed_question = {
                'question': question_info['text'],
                'type': analysis['type'],
                'complexity': analysis['complexity'],
                'entities': analysis['entities'],
                'structure': {
                    'number': question_info['number'],
                    'section': question_info['section'],
                    'marks': question_info['marks'],
                    'sub_questions': question_info['sub_questions']
                },
                'source': {
                    'type': 'question_paper',
                    'path': document_path,
                    'format': ext[1:]
                }
            }
            
            processed_questions.append(processed_question)
        
        return processed_questions
    
    def _identify_question_paper_structure(self, text: str) -> List[Dict[str, Any]]:
        """
        Identify the structure of a question paper.
        
        Args:
            text: Extracted text from the question paper
            
        Returns:
            List of questions with structure information
        """
        # Split text into lines
        lines = text.split('\n')
        
        # Initialize variables
        current_section = "Default"
        questions = []
        
        # Regular expressions for identifying question patterns
        question_patterns = [
            r'^(?:Q|Question)\.?\s*(\d+)\.?\s*(.*?)(?:\((\d+)\s*marks\))?$',  # Q1. What is...? (5 marks)
            r'^(\d+)\.?\s*(.*?)(?:\((\d+)\s*marks\))?$',  # 1. What is...? (5 marks)
            r'^(?:Q|Question)\.?\s*(\d+)\.?\s*\((\d+)\s*marks\)\s*(.*?)$',  # Q1. (5 marks) What is...?
            r'^(\d+)\.?\s*\((\d+)\s*marks\)\s*(.*?)$'  # 1. (5 marks) What is...?
        ]
        
        # Regular expression for identifying sections
        section_pattern = r'^(?:Section|SECTION|Part|PART)\s*[:-]?\s*([A-Z0-9]+)'
        
        # Process each line
        i = 0
        while i < len(lines):
            line = lines[i].strip()
            
            # Check if line indicates a section
            section_match = re.search(section_pattern, line)
            if section_match:
                current_section = section_match.group(1)
                i += 1
                continue
            
            # Check if line contains a question
            question_found = False
            for pattern in question_patterns:
                match = re.search(pattern, line)
                if match:
                    question_found = True
                    
                    # Extract question number and text
                    if len(match.groups()) == 3:
                        question_num = match.group(1)
                        question_text = match.group(2) if match.group(3) is None else match.group(3)
                        marks = match.group(3) if match.group(3) is not None else match.group(2)
                    else:
                        question_num = match.group(1)
                        question_text = match.group(2)
                        marks = None
                    
                    # Try to get more of the question text from subsequent lines
                    full_question_text = question_text
                    j = i + 1
                    sub_questions = []
                    
                    while j < len(lines) and not any(re.search(p, lines[j].strip()) for p in question_patterns):
                        # Check for sub-questions
                        sub_q_match = re.search(r'^(?:[a-z]|\([a-z]\)|\([ivx]+\))\)\s*(.*?)$', lines[j].strip())
                        if sub_q_match:
                            sub_questions.append(sub_q_match.group(1))
                        else:
                            # Add to main question text if not a sub-question and not empty
                            if lines[j].strip():
                                full_question_text += " " + lines[j].strip()
                        j += 1
                    
                    # Add question to list
                    questions.append({
                        'number': question_num,
                        'text': full_question_text,
                        'section': current_section,
                        'marks': marks,
                        'sub_questions': sub_questions
                    })
                    
                    # Update index to skip processed lines
                    i = j - 1
                    break
            
            if not question_found:
                i += 1
        
        return questions
    
    def _extract_text_from_pdf(self, pdf_path: str) -> str:
        """
        Extract text from a PDF file.
        
        Args:
            pdf_path: Path to the PDF file
            
        Returns:
            Extracted text from the PDF
        """
        try:
            text = ""
            # Open the PDF
            with fitz.open(pdf_path) as doc:
                # Iterate through pages
                for page in doc:
                    # Extract text from page
                    text += page.get_text()
            
            return text
        except Exception as e:
            print(f"Error extracting text from PDF: {e}")
            return ""
    
    def _extract_text_from_docx(self, docx_path: str) -> str:
        """
        Extract text from a DOCX file.
        
        Args:
            docx_path: Path to the DOCX file
            
        Returns:
            Extracted text from the DOCX
        """
        try:
            text = ""
            # Open the DOCX
            doc = docx.Document(docx_path)
            
            # Iterate through paragraphs
            for para in doc.paragraphs:
                text += para.text + "\n"
            
            return text
        except Exception as e:
            print(f"Error extracting text from DOCX: {e}")
            return ""
    
    def _extract_text_from_txt(self, txt_path: str) -> str:
        """
        Extract text from a TXT file.
        
        Args:
            txt_path: Path to the TXT file
            
        Returns:
            Extracted text from the TXT
        """
        try:
            # Open the TXT file
            with open(txt_path, 'r', encoding='utf-8') as file:
                text = file.read()
            
            return text
        except Exception as e:
            print(f"Error extracting text from TXT: {e}")
            return ""
    
    def _extract_text_from_csv(self, csv_path: str) -> str:
        """
        Extract text from a CSV file.
        
        Args:
            csv_path: Path to the CSV file
            
        Returns:
            Extracted text from the CSV
        """
        try:
            text = ""
            # Open the CSV file
            with open(csv_path, 'r', encoding='utf-8') as file:
                reader = csv.reader(file)
                # Iterate through rows
                for row in reader:
                    text += " ".join(row) + "\n"
            
            return text
        except Exception as e:
            print(f"Error extracting text from CSV: {e}")
            return ""


# Example usage
if __name__ == "__main__":
    processor = DocumentProcessor()
    
    # Example document path
    document_path = "sample_question_paper.pdf"
    
    # Check if file exists
    if os.path.exists(document_path):
        # Process as a question paper
        results = processor.process_question_paper(document_path)
        
        print(f"Extracted {len(results)} questions from question paper:")
        for i, result in enumerate(results, 1):
            print(f"\nQuestion {i}: {result['question']}")
            print(f"Number: {result['structure']['number']}")
            print(f"Section: {result['structure']['section']}")
            print(f"Marks: {result['structure']['marks']}")
            print(f"Type: {result['type']}")
            print(f"Complexity: {result['complexity']}")
            
            if result['structure']['sub_questions']:
                print("Sub-questions:")
                for j, sub_q in enumerate(result['structure']['sub_questions'], 1):
                    print(f"  {j}. {sub_q}")
    else:
        print(f"Document file {document_path} not found.")
