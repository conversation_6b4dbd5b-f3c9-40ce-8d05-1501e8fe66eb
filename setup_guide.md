# WhatsApp AI Agent Setup Guide

## Overview

This document provides a comprehensive guide for setting up and deploying the WhatsApp AI Agent with pay-as-you-go functionality. The system allows users to send questions via WhatsApp in the form of text, images, or documents (including question papers) and receive accurate answers, with the option to get responses in PDF format.

## System Components

The WhatsApp AI Agent consists of the following main components:

1. **Input Processing Modules**
   - Text Processing
   - Image Processing
   - Document Processing

2. **Answer Generation System**
   - Single Question Answering
   - Bulk Question Paper Processing

3. **PDF Generation Service**
   - Single Answer PDF
   - Bulk Answers PDF

4. **Payment System**
   - User Management
   - Payment Processing
   - Credit System

5. **WhatsApp Integration**
   - Message Handling
   - User Verification
   - Access Control

## Prerequisites

Before setting up the system, ensure you have the following:

- A server with Python 3.8+ installed
- WhatsApp Business API account
- Payment gateway account (Stripe, PayPal, etc.)
- Domain name for the web portal
- SSL certificate for secure connections
- Database server (SQLite for development, PostgreSQL/MySQL for production)

## Installation Steps

### 1. Clone the Repository

```bash
git clone https://github.com/yourusername/whatsapp-ai-agent.git
cd whatsapp-ai-agent
```

### 2. Set Up Virtual Environment

```bash
python -m venv venv
source venv/bin/activate  # On Windows: venv\Scripts\activate
pip install -r requirements.txt
```

### 3. Configure Environment Variables

Create a `.env` file in the root directory with the following variables:

```
# WhatsApp API Configuration
WHATSAPP_API_URL=https://graph.facebook.com/v17.0/YOUR_PHONE_NUMBER_ID/messages
WHATSAPP_API_KEY=your_whatsapp_api_key

# Payment Gateway Configuration
PAYMENT_API_KEY=your_payment_gateway_api_key
PAYMENT_SECRET_KEY=your_payment_gateway_secret_key

# Database Configuration
DATABASE_URL=sqlite:///whatsapp_ai_agent.db  # For development
# DATABASE_URL=postgresql://user:password@localhost/whatsapp_ai_agent  # For production

# LLM API Configuration
LLM_API_URL=https://api.openai.com/v1/chat/completions
LLM_API_KEY=your_llm_api_key

# Web Portal Configuration
WEB_DOMAIN=https://your-domain.com
SECRET_KEY=your_secret_key_for_sessions
```

### 4. Initialize the Database

```bash
python scripts/init_database.py
```

### 5. Set Up the Web Portal

```bash
# Install web dependencies
cd web_portal
npm install
npm run build
cd ..

# Configure web server (Nginx example)
sudo cp config/nginx.conf /etc/nginx/sites-available/whatsapp-ai-agent
sudo ln -s /etc/nginx/sites-available/whatsapp-ai-agent /etc/nginx/sites-enabled/
sudo systemctl restart nginx
```

### 6. Configure WhatsApp Business API

1. Log in to your WhatsApp Business API account
2. Set up a webhook to receive messages at `https://your-domain.com/api/webhook`
3. Configure message templates for system notifications

### 7. Start the Services

```bash
# Start the main application server
python app.py

# Start the worker processes (in separate terminals or as services)
python workers/text_processor_worker.py
python workers/image_processor_worker.py
python workers/document_processor_worker.py
python workers/answer_generator_worker.py
python workers/pdf_generator_worker.py
```

For production, set up these services using Supervisor or systemd.

## System Configuration

### Credit System Configuration

Edit `config/credits.json` to configure the credit costs for different services:

```json
{
  "text_question": 1.0,
  "image_question": 2.0,
  "document_processing": 5.0,
  "question_paper_processing": 10.0,
  "pdf_generation": 2.0
}
```

### WhatsApp Message Templates

Configure message templates in the WhatsApp Business API dashboard for:

1. Welcome message
2. Verification code message
3. Credit balance notification
4. Service usage confirmation

### Payment Plans

Edit `config/payment_plans.json` to configure available payment plans:

```json
{
  "plans": [
    {
      "id": "basic",
      "name": "Basic Plan",
      "credits": 50,
      "price": 9.99,
      "description": "Perfect for occasional use"
    },
    {
      "id": "standard",
      "name": "Standard Plan",
      "credits": 150,
      "price": 24.99,
      "description": "Ideal for regular users"
    },
    {
      "id": "premium",
      "name": "Premium Plan",
      "credits": 500,
      "price": 69.99,
      "description": "Best value for power users"
    }
  ]
}
```

## User Onboarding Flow

The typical user onboarding flow is as follows:

1. User visits the web portal and creates an account
2. User purchases credits through the payment system
3. User registers their WhatsApp number
4. User receives a verification code via the web portal
5. User sends the verification code to the WhatsApp business number
6. System verifies the WhatsApp number and activates the account
7. User can now send questions via WhatsApp

## Usage Examples

### Sending a Text Question

User simply sends a text message with their question:
```
What is the capital of France?
```

### Sending an Image Question

User sends an image containing text with questions, such as a screenshot of a textbook page.

### Sending a Document

User sends a document file (PDF, DOCX, etc.) containing questions.

### Requesting PDF Output

After receiving an answer, user can request a PDF version by replying:
```
PDF please
```

## Monitoring and Maintenance

### Logs

All system logs are stored in the `logs/` directory. Monitor these regularly for errors or issues.

### Database Backup

Set up regular database backups:

```bash
# Example backup script
python scripts/backup_database.py
```

### System Updates

To update the system:

```bash
git pull
pip install -r requirements.txt
python scripts/migrate_database.py
sudo systemctl restart whatsapp-ai-agent
```

## Troubleshooting

### Common Issues

1. **WhatsApp API Connection Issues**
   - Check API credentials
   - Verify webhook configuration
   - Ensure server is accessible from the internet

2. **Payment Processing Failures**
   - Check payment gateway credentials
   - Verify webhook endpoints
   - Check for declined transactions in payment gateway dashboard

3. **Message Processing Errors**
   - Check worker process logs
   - Ensure all dependencies are installed
   - Verify LLM API connectivity

## Security Considerations

1. **Data Protection**
   - All user data is encrypted at rest
   - Payment information is handled securely through the payment gateway
   - Messages are processed in memory and not stored permanently

2. **API Security**
   - All API endpoints are protected with authentication
   - Rate limiting is implemented to prevent abuse
   - Input validation is performed on all user inputs

3. **Access Control**
   - Only verified users with sufficient credits can access the service
   - Admin access is protected with multi-factor authentication

## Support and Contact

For support or questions, contact:
- Email: <EMAIL>
- Phone: ******-567-8910
- Web: https://your-domain.com/support

## License and Legal Information

This system is licensed under [Your License]. See LICENSE.txt for details.

Payment processing is subject to the terms and conditions of the payment gateway provider.

WhatsApp usage is subject to the WhatsApp Business API terms of service.
