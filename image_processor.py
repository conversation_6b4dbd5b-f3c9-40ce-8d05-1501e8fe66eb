"""
Image Processing Module for WhatsApp AI Agent

This module handles the processing of image-based questions received through WhatsApp.
It performs OCR on images, extracts text content, identifies questions, and prepares them
for the answer generation module.
"""

import os
import cv2
import numpy as np
import pytesseract
from PIL import Image
from typing import List, Dict, Any, Union
import io
import base64
import re

# Import the text processor to reuse question extraction logic
import sys
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from text_processing.text_processor import TextProcessor

class ImageProcessor:
    """Class for processing image-based questions from WhatsApp messages."""
    
    def __init__(self):
        """Initialize the image processor."""
        self.text_processor = TextProcessor()
        
    def process_image(self, image_path: str) -> List[Dict[str, Any]]:
        """
        Process an image to extract text and questions.
        
        Args:
            image_path: Path to the image file
            
        Returns:
            List of processed questions with analysis
        """
        # Extract text from image
        extracted_text = self._extract_text_from_image(image_path)
        
        # If no text was extracted, return empty list
        if not extracted_text:
            return []
        
        # Use text processor to extract and analyze questions
        processed_questions = self.text_processor.process_text_input(extracted_text)
        
        # Add source information
        for question in processed_questions:
            question['source'] = {
                'type': 'image',
                'path': image_path,
                'extracted_text': extracted_text
            }
        
        return processed_questions
    
    def _extract_text_from_image(self, image_path: str) -> str:
        """
        Extract text from an image using OCR.
        
        Args:
            image_path: Path to the image file
            
        Returns:
            Extracted text from the image
        """
        try:
            # Load image
            image = cv2.imread(image_path)
            if image is None:
                print(f"Error: Could not load image from {image_path}")
                return ""
            
            # Preprocess image for better OCR results
            preprocessed_image = self._preprocess_image(image)
            
            # Perform OCR
            text = pytesseract.image_to_string(preprocessed_image)
            
            return text.strip()
        except Exception as e:
            print(f"Error extracting text from image: {e}")
            return ""
    
    def _preprocess_image(self, image: np.ndarray) -> np.ndarray:
        """
        Preprocess image to improve OCR accuracy.
        
        Args:
            image: Input image as numpy array
            
        Returns:
            Preprocessed image
        """
        # Convert to grayscale
        gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
        
        # Apply thresholding to get black and white image
        _, binary = cv2.threshold(gray, 150, 255, cv2.THRESH_BINARY_INV + cv2.THRESH_OTSU)
        
        # Invert image to get black text on white background
        binary = cv2.bitwise_not(binary)
        
        # Apply noise reduction
        kernel = np.ones((1, 1), np.uint8)
        binary = cv2.morphologyEx(binary, cv2.MORPH_OPEN, kernel)
        
        return binary
    
    def process_image_from_base64(self, base64_string: str) -> List[Dict[str, Any]]:
        """
        Process an image from base64 string to extract text and questions.
        
        Args:
            base64_string: Base64 encoded image
            
        Returns:
            List of processed questions with analysis
        """
        try:
            # Decode base64 string
            image_data = base64.b64decode(base64_string)
            
            # Convert to numpy array
            nparr = np.frombuffer(image_data, np.uint8)
            
            # Decode image
            image = cv2.imdecode(nparr, cv2.IMREAD_COLOR)
            
            # Preprocess image
            preprocessed_image = self._preprocess_image(image)
            
            # Perform OCR
            text = pytesseract.image_to_string(preprocessed_image)
            
            # Process extracted text
            if not text.strip():
                return []
            
            # Use text processor to extract and analyze questions
            processed_questions = self.text_processor.process_text_input(text.strip())
            
            # Add source information
            for question in processed_questions:
                question['source'] = {
                    'type': 'image',
                    'format': 'base64',
                    'extracted_text': text.strip()
                }
            
            return processed_questions
        except Exception as e:
            print(f"Error processing base64 image: {e}")
            return []
    
    def detect_handwritten_text(self, image_path: str) -> str:
        """
        Detect handwritten text in an image (specialized OCR).
        
        Args:
            image_path: Path to the image file
            
        Returns:
            Extracted handwritten text
        """
        try:
            # Load image
            image = cv2.imread(image_path)
            
            # Convert to grayscale
            gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
            
            # Apply adaptive thresholding
            thresh = cv2.adaptiveThreshold(gray, 255, cv2.ADAPTIVE_THRESH_GAUSSIAN_C, 
                                          cv2.THRESH_BINARY_INV, 11, 2)
            
            # Noise removal
            kernel = np.ones((1, 1), np.uint8)
            opening = cv2.morphologyEx(thresh, cv2.MORPH_OPEN, kernel)
            
            # OCR with specific config for handwritten text
            config = '--psm 6 --oem 3'  # Page segmentation mode 6: Assume a single uniform block of text
            text = pytesseract.image_to_string(opening, config=config)
            
            return text.strip()
        except Exception as e:
            print(f"Error detecting handwritten text: {e}")
            return ""
    
    def extract_math_equations(self, image_path: str) -> List[str]:
        """
        Extract mathematical equations from an image.
        
        Args:
            image_path: Path to the image file
            
        Returns:
            List of extracted equations
        """
        # Extract all text first
        text = self._extract_text_from_image(image_path)
        
        # Define patterns for mathematical equations
        equation_patterns = [
            r'\$.*?\$',  # LaTeX inline equations
            r'\$\$.*?\$\$',  # LaTeX display equations
            r'[0-9]+\s*[\+\-\*\/\=]\s*[0-9]+',  # Simple arithmetic
            r'[a-zA-Z]\s*[\+\-\*\/\=]\s*[0-9a-zA-Z]',  # Algebraic equations
            r'\\frac\{.*?\}\{.*?\}',  # LaTeX fractions
            r'\\sqrt\{.*?\}',  # LaTeX square roots
            r'\\int',  # LaTeX integrals
            r'\\sum',  # LaTeX summations
            r'[0-9a-zA-Z\+\-\*\/\=\(\)\[\]\{\}\^\_\s]+'  # General pattern for equations
        ]
        
        equations = []
        for pattern in equation_patterns:
            matches = re.findall(pattern, text)
            equations.extend(matches)
        
        # Remove duplicates and empty strings
        equations = list(set([eq.strip() for eq in equations if eq.strip()]))
        
        return equations


# Example usage
if __name__ == "__main__":
    processor = ImageProcessor()
    
    # Example image path
    image_path = "sample_question.jpg"
    
    # Check if file exists
    if os.path.exists(image_path):
        results = processor.process_image(image_path)
        
        print(f"Extracted {len(results)} questions from image:")
        for i, result in enumerate(results, 1):
            print(f"\nQuestion {i}: {result['question']}")
            print(f"Type: {result['type']}")
            print(f"Complexity: {result['complexity']}")
            print(f"Key entities: {', '.join(result['entities'])}")
    else:
        print(f"Image file {image_path} not found.")
