"""
Payment System for WhatsApp AI Agent

This module implements the payment system for the WhatsApp AI agent, supporting
pay-as-you-go functionality, user management, and usage tracking.
"""

import os
import sys
import json
import time
import uuid
import hashlib
from datetime import datetime
from typing import List, Dict, Any, Union, Tuple
import sqlite3

class PaymentSystem:
    """Class for managing payments, user accounts, and usage tracking."""
    
    def __init__(self, db_path: str = "payment_system.db"):
        """
        Initialize the payment system.
        
        Args:
            db_path: Path to the SQLite database file
        """
        self.db_path = db_path
        self._initialize_database()
    
    def _initialize_database(self):
        """Initialize the database with necessary tables."""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        # Create users table
        cursor.execute('''
        CREATE TABLE IF NOT EXISTS users (
            id TEXT PRIMARY KEY,
            username TEXT UNIQUE NOT NULL,
            email TEXT UNIQUE NOT NULL,
            password_hash TEXT NOT NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            last_login TIMESTAMP,
            is_active INTEGER DEFAULT 1
        )
        ''')
        
        # Create payment_methods table
        cursor.execute('''
        CREATE TABLE IF NOT EXISTS payment_methods (
            id TEXT PRIMARY KEY,
            user_id TEXT NOT NULL,
            payment_type TEXT NOT NULL,
            card_last_four TEXT,
            expiry_date TEXT,
            is_default INTEGER DEFAULT 0,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (user_id) REFERENCES users (id)
        )
        ''')
        
        # Create credits table
        cursor.execute('''
        CREATE TABLE IF NOT EXISTS credits (
            id TEXT PRIMARY KEY,
            user_id TEXT NOT NULL,
            balance REAL NOT NULL DEFAULT 0,
            last_updated TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (user_id) REFERENCES users (id)
        )
        ''')
        
        # Create transactions table
        cursor.execute('''
        CREATE TABLE IF NOT EXISTS transactions (
            id TEXT PRIMARY KEY,
            user_id TEXT NOT NULL,
            amount REAL NOT NULL,
            transaction_type TEXT NOT NULL,
            description TEXT,
            payment_method_id TEXT,
            status TEXT NOT NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (user_id) REFERENCES users (id),
            FOREIGN KEY (payment_method_id) REFERENCES payment_methods (id)
        )
        ''')
        
        # Create usage_logs table
        cursor.execute('''
        CREATE TABLE IF NOT EXISTS usage_logs (
            id TEXT PRIMARY KEY,
            user_id TEXT NOT NULL,
            service_type TEXT NOT NULL,
            credits_used REAL NOT NULL,
            details TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (user_id) REFERENCES users (id)
        )
        ''')
        
        # Create whatsapp_verification table
        cursor.execute('''
        CREATE TABLE IF NOT EXISTS whatsapp_verification (
            id TEXT PRIMARY KEY,
            user_id TEXT NOT NULL,
            phone_number TEXT UNIQUE NOT NULL,
            verification_code TEXT NOT NULL,
            is_verified INTEGER DEFAULT 0,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            verified_at TIMESTAMP,
            FOREIGN KEY (user_id) REFERENCES users (id)
        )
        ''')
        
        conn.commit()
        conn.close()
    
    def register_user(self, username: str, email: str, password: str) -> Dict[str, Any]:
        """
        Register a new user.
        
        Args:
            username: User's username
            email: User's email address
            password: User's password
            
        Returns:
            Dictionary with registration result
        """
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # Check if username or email already exists
            cursor.execute("SELECT id FROM users WHERE username = ? OR email = ?", (username, email))
            existing_user = cursor.fetchone()
            
            if existing_user:
                return {
                    'success': False,
                    'message': 'Username or email already exists'
                }
            
            # Generate user ID
            user_id = str(uuid.uuid4())
            
            # Hash password
            password_hash = hashlib.sha256(password.encode()).hexdigest()
            
            # Insert user
            cursor.execute(
                "INSERT INTO users (id, username, email, password_hash) VALUES (?, ?, ?, ?)",
                (user_id, username, email, password_hash)
            )
            
            # Initialize credits for user
            cursor.execute(
                "INSERT INTO credits (id, user_id, balance) VALUES (?, ?, ?)",
                (str(uuid.uuid4()), user_id, 0.0)
            )
            
            conn.commit()
            
            return {
                'success': True,
                'user_id': user_id,
                'message': 'User registered successfully'
            }
        except Exception as e:
            return {
                'success': False,
                'message': f'Error registering user: {str(e)}'
            }
        finally:
            conn.close()
    
    def authenticate_user(self, username_or_email: str, password: str) -> Dict[str, Any]:
        """
        Authenticate a user.
        
        Args:
            username_or_email: User's username or email
            password: User's password
            
        Returns:
            Dictionary with authentication result
        """
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # Hash password
            password_hash = hashlib.sha256(password.encode()).hexdigest()
            
            # Check credentials
            cursor.execute(
                "SELECT id, username, email FROM users WHERE (username = ? OR email = ?) AND password_hash = ?",
                (username_or_email, username_or_email, password_hash)
            )
            user = cursor.fetchone()
            
            if not user:
                return {
                    'success': False,
                    'message': 'Invalid credentials'
                }
            
            # Update last login
            cursor.execute(
                "UPDATE users SET last_login = CURRENT_TIMESTAMP WHERE id = ?",
                (user[0],)
            )
            
            conn.commit()
            
            return {
                'success': True,
                'user_id': user[0],
                'username': user[1],
                'email': user[2],
                'message': 'Authentication successful'
            }
        except Exception as e:
            return {
                'success': False,
                'message': f'Error authenticating user: {str(e)}'
            }
        finally:
            conn.close()
    
    def add_payment_method(self, user_id: str, payment_type: str, card_number: str = None,
                          expiry_date: str = None, cvv: str = None, is_default: bool = False) -> Dict[str, Any]:
        """
        Add a payment method for a user.
        
        Args:
            user_id: User's ID
            payment_type: Type of payment method (e.g., 'credit_card', 'paypal')
            card_number: Credit card number (for credit_card type)
            expiry_date: Expiry date (for credit_card type)
            cvv: CVV code (for credit_card type)
            is_default: Whether this is the default payment method
            
        Returns:
            Dictionary with result
        """
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # Check if user exists
            cursor.execute("SELECT id FROM users WHERE id = ?", (user_id,))
            user = cursor.fetchone()
            
            if not user:
                return {
                    'success': False,
                    'message': 'User not found'
                }
            
            # Generate payment method ID
            payment_method_id = str(uuid.uuid4())
            
            # Process card details if payment type is credit_card
            card_last_four = None
            if payment_type == 'credit_card' and card_number:
                # In a real implementation, you would validate the card and use a payment processor
                # Here we just store the last four digits
                card_last_four = card_number[-4:] if len(card_number) >= 4 else card_number
            
            # If this is set as default, unset any existing default
            if is_default:
                cursor.execute(
                    "UPDATE payment_methods SET is_default = 0 WHERE user_id = ?",
                    (user_id,)
                )
            
            # Insert payment method
            cursor.execute(
                "INSERT INTO payment_methods (id, user_id, payment_type, card_last_four, expiry_date, is_default) VALUES (?, ?, ?, ?, ?, ?)",
                (payment_method_id, user_id, payment_type, card_last_four, expiry_date, 1 if is_default else 0)
            )
            
            conn.commit()
            
            return {
                'success': True,
                'payment_method_id': payment_method_id,
                'message': 'Payment method added successfully'
            }
        except Exception as e:
            return {
                'success': False,
                'message': f'Error adding payment method: {str(e)}'
            }
        finally:
            conn.close()
    
    def purchase_credits(self, user_id: str, amount: float, payment_method_id: str = None) -> Dict[str, Any]:
        """
        Purchase credits for a user.
        
        Args:
            user_id: User's ID
            amount: Amount of credits to purchase
            payment_method_id: ID of the payment method to use (if None, uses default)
            
        Returns:
            Dictionary with result
        """
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # Check if user exists
            cursor.execute("SELECT id FROM users WHERE id = ?", (user_id,))
            user = cursor.fetchone()
            
            if not user:
                return {
                    'success': False,
                    'message': 'User not found'
                }
            
            # Get payment method
            if not payment_method_id:
                cursor.execute(
                    "SELECT id FROM payment_methods WHERE user_id = ? AND is_default = 1",
                    (user_id,)
                )
                payment_method = cursor.fetchone()
                
                if not payment_method:
                    return {
                        'success': False,
                        'message': 'No default payment method found'
                    }
                
                payment_method_id = payment_method[0]
            else:
                cursor.execute(
                    "SELECT id FROM payment_methods WHERE id = ? AND user_id = ?",
                    (payment_method_id, user_id)
                )
                payment_method = cursor.fetchone()
                
                if not payment_method:
                    return {
                        'success': False,
                        'message': 'Payment method not found'
                    }
            
            # In a real implementation, you would process the payment through a payment processor
            # Here we assume the payment is successful
            
            # Generate transaction ID
            transaction_id = str(uuid.uuid4())
            
            # Record transaction
            cursor.execute(
                "INSERT INTO transactions (id, user_id, amount, transaction_type, description, payment_method_id, status) VALUES (?, ?, ?, ?, ?, ?, ?)",
                (transaction_id, user_id, amount, 'purchase', f'Purchase of {amount} credits', payment_method_id, 'completed')
            )
            
            # Update credits balance
            cursor.execute(
                "UPDATE credits SET balance = balance + ?, last_updated = CURRENT_TIMESTAMP WHERE user_id = ?",
                (amount, user_id)
            )
            
            conn.commit()
            
            # Get updated balance
            cursor.execute("SELECT balance FROM credits WHERE user_id = ?", (user_id,))
            balance = cursor.fetchone()[0]
            
            return {
                'success': True,
                'transaction_id': transaction_id,
                'credits_purchased': amount,
                'new_balance': balance,
                'message': f'Successfully purchased {amount} credits'
            }
        except Exception as e:
            return {
                'success': False,
                'message': f'Error purchasing credits: {str(e)}'
            }
        finally:
            conn.close()
    
    def check_credit_balance(self, user_id: str) -> Dict[str, Any]:
        """
        Check the credit balance for a user.
        
        Args:
            user_id: User's ID
            
        Returns:
            Dictionary with result
        """
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # Check if user exists
            cursor.execute("SELECT id FROM users WHERE id = ?", (user_id,))
            user = cursor.fetchone()
            
            if not user:
                return {
                    'success': False,
                    'message': 'User not found'
                }
            
            # Get credit balance
            cursor.execute("SELECT balance, last_updated FROM credits WHERE user_id = ?", (user_id,))
            credit_info = cursor.fetchone()
            
            if not credit_info:
                return {
                    'success': False,
                    'message': 'Credit information not found'
                }
            
            return {
                'success': True,
                'balance': credit_info[0],
                'last_updated': credit_info[1],
                'message': f'Current balance: {credit_info[0]} credits'
            }
        except Exception as e:
            return {
                'success': False,
                'message': f'Error checking credit balance: {str(e)}'
            }
        finally:
            conn.close()
    
    def use_credits(self, user_id: str, amount: float, service_type: str, details: str = None) -> Dict[str, Any]:
        """
        Use credits for a service.
        
        Args:
            user_id: User's ID
            amount: Amount of credits to use
            service_type: Type of service (e.g., 'text_question', 'image_question', 'document_processing', 'pdf_generation')
            details: Additional details about the usage
            
        Returns:
            Dictionary with result
        """
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # Check if user exists
            cursor.execute("SELECT id FROM users WHERE id = ?", (user_id,))
            user = cursor.fetchone()
            
            if not user:
     
(Content truncated due to size limit. Use line ranges to read in chunks)