"""
PDF Generation Module for WhatsApp AI Agent

This module handles the generation of PDF documents containing answers to questions.
It supports both single question answers and bulk question paper answers, with proper
formatting and structure.
"""

import os
import sys
from typing import List, Dict, Any, Union, Tuple
import time
from datetime import datetime
from weasyprint import HTML, CSS
from weasyprint.text.fonts import FontConfiguration
import json
import tempfile

class PDFGenerator:
    """Class for generating PDF documents with answers."""
    
    def __init__(self):
        """Initialize the PDF generator."""
        self.font_config = FontConfiguration()
        self.default_css = CSS(string='''
            @page {
                margin: 1cm;
            }
            body {
                font-family: "Noto Sans CJK SC", "WenQuanYi Zen Hei", sans-serif;
                line-height: 1.5;
                color: #333;
            }
            h1 {
                color: #2c3e50;
                font-size: 24px;
                margin-top: 20px;
                margin-bottom: 10px;
                page-break-after: avoid;
            }
            h2 {
                color: #34495e;
                font-size: 20px;
                margin-top: 15px;
                margin-bottom: 10px;
                page-break-after: avoid;
            }
            h3 {
                color: #7f8c8d;
                font-size: 16px;
                margin-top: 10px;
                margin-bottom: 5px;
                page-break-after: avoid;
            }
            p {
                margin-bottom: 10px;
            }
            .question {
                background-color: #f8f9fa;
                border-left: 4px solid #4285f4;
                padding: 10px;
                margin-bottom: 15px;
            }
            .answer {
                background-color: #f1f8e9;
                border-left: 4px solid #0f9d58;
                padding: 10px;
                margin-bottom: 20px;
            }
            .metadata {
                color: #666;
                font-size: 12px;
                margin-bottom: 5px;
            }
            .footer {
                text-align: center;
                font-size: 10px;
                color: #999;
                margin-top: 20px;
                border-top: 1px solid #eee;
                padding-top: 10px;
            }
            .header {
                text-align: center;
                margin-bottom: 20px;
                border-bottom: 1px solid #eee;
                padding-bottom: 10px;
            }
            .section {
                margin-top: 30px;
                margin-bottom: 20px;
            }
            .page-break {
                page-break-after: always;
            }
            .confidence {
                color: #999;
                font-style: italic;
                font-size: 12px;
            }
            .sources {
                font-size: 12px;
                color: #666;
                margin-top: 10px;
            }
        ''', font_config=self.font_config)
    
    def generate_single_answer_pdf(self, question: str, answer: str, output_path: str, 
                                  metadata: Dict[str, Any] = None) -> str:
        """
        Generate a PDF document for a single question and answer.
        
        Args:
            question: The question text
            answer: The answer text
            output_path: Path to save the PDF file
            metadata: Optional metadata to include in the PDF
            
        Returns:
            Path to the generated PDF file
        """
        # Create HTML content
        html_content = f"""
        <!DOCTYPE html>
        <html>
        <head>
            <meta charset="UTF-8">
            <title>Question Answer</title>
        </head>
        <body>
            <div class="header">
                <h1>Question Answer</h1>
                <p class="metadata">Generated on {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}</p>
            </div>
            
            <div class="section">
                <div class="question">
                    <h2>Question</h2>
                    <p>{question}</p>
                </div>
                
                <div class="answer">
                    <h2>Answer</h2>
                    <p>{answer}</p>
                </div>
            </div>
        """
        
        # Add metadata if provided
        if metadata:
            html_content += '<div class="section">\n<h3>Additional Information</h3>\n'
            for key, value in metadata.items():
                html_content += f'<p class="metadata"><strong>{key}:</strong> {value}</p>\n'
            html_content += '</div>\n'
        
        # Add footer
        html_content += """
            <div class="footer">
                <p>Generated by WhatsApp AI Agent</p>
            </div>
        </body>
        </html>
        """
        
        # Generate PDF
        try:
            HTML(string=html_content).write_pdf(
                output_path,
                stylesheets=[self.default_css],
                font_config=self.font_config
            )
            return output_path
        except Exception as e:
            print(f"Error generating PDF: {e}")
            return ""
    
    def generate_bulk_answers_pdf(self, questions_with_answers: List[Dict[str, Any]], 
                                 output_path: str, title: str = "Question Paper Answers") -> str:
        """
        Generate a PDF document for multiple questions and answers from a question paper.
        
        Args:
            questions_with_answers: List of dictionaries containing questions and answers
            output_path: Path to save the PDF file
            title: Title for the PDF document
            
        Returns:
            Path to the generated PDF file
        """
        # Create HTML content
        html_content = f"""
        <!DOCTYPE html>
        <html>
        <head>
            <meta charset="UTF-8">
            <title>{title}</title>
        </head>
        <body>
            <div class="header">
                <h1>{title}</h1>
                <p class="metadata">Generated on {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}</p>
                <p class="metadata">Total Questions: {len(questions_with_answers)}</p>
            </div>
        """
        
        # Group questions by section
        sections = {}
        for qa in questions_with_answers:
            section = qa.get('section', 'Default')
            if section not in sections:
                sections[section] = []
            sections[section].append(qa)
        
        # Add each section
        for section, section_qas in sections.items():
            html_content += f"""
            <div class="section">
                <h2>Section {section}</h2>
            """
            
            # Add each question and answer
            for qa in section_qas:
                question_number = qa.get('question_number', '')
                marks = qa.get('marks', '')
                question_text = qa.get('question_text', '')
                answer = qa.get('answer', '')
                confidence = qa.get('confidence', '')
                sources = qa.get('sources', [])
                
                html_content += f"""
                <div class="question">
                    <h3>Question {question_number} {f'({marks} marks)' if marks else ''}</h3>
                    <p>{question_text}</p>
                </div>
                
                <div class="answer">
                    <h3>Answer</h3>
                    <p>{answer}</p>
                """
                
                if confidence:
                    html_content += f'<p class="confidence">Confidence: {confidence:.2f}</p>\n'
                
                if sources:
                    html_content += '<div class="sources"><p><strong>Sources:</strong></p><ul>\n'
                    for source in sources:
                        html_content += f'<li>{source}</li>\n'
                    html_content += '</ul></div>\n'
                
                html_content += '</div>\n'
            
            html_content += '</div>\n'
            
            # Add page break after each section except the last one
            if section != list(sections.keys())[-1]:
                html_content += '<div class="page-break"></div>\n'
        
        # Add footer
        html_content += """
            <div class="footer">
                <p>Generated by WhatsApp AI Agent</p>
            </div>
        </body>
        </html>
        """
        
        # Generate PDF
        try:
            HTML(string=html_content).write_pdf(
                output_path,
                stylesheets=[self.default_css],
                font_config=self.font_config
            )
            return output_path
        except Exception as e:
            print(f"Error generating PDF: {e}")
            return ""
    
    def generate_pdf_from_json(self, json_path: str, output_path: str) -> str:
        """
        Generate a PDF document from a JSON file containing questions and answers.
        
        Args:
            json_path: Path to the JSON file
            output_path: Path to save the PDF file
            
        Returns:
            Path to the generated PDF file
        """
        try:
            # Load JSON data
            with open(json_path, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            # Check if it's a bulk question paper result
            if 'questions_with_answers' in data:
                return self.generate_bulk_answers_pdf(
                    data['questions_with_answers'],
                    output_path,
                    title=f"Answers for {os.path.basename(data.get('document_path', 'Question Paper'))}"
                )
            # Check if it's a single question-answer pair
            elif 'question' in data and 'answer' in data:
                return self.generate_single_answer_pdf(
                    data['question'],
                    data['answer'],
                    output_path,
                    metadata=data.get('metadata', {})
                )
            else:
                print("Error: Unsupported JSON format")
                return ""
        except Exception as e:
            print(f"Error generating PDF from JSON: {e}")
            return ""
    
    def track_pdf_generation(self, user_id: str, output_path: str, num_pages: int) -> Dict[str, Any]:
        """
        Track PDF generation for billing purposes.
        
        Args:
            user_id: ID of the user
            output_path: Path to the generated PDF
            num_pages: Number of pages in the PDF
            
        Returns:
            Dictionary containing usage information
        """
        # This is a placeholder for the actual usage tracking system
        # In the real implementation, this would connect to a database
        usage_info = {
            'user_id': user_id,
            'timestamp': time.time(),
            'pdf_path': output_path,
            'num_pages': num_pages,
            'service_type': 'pdf_generation',
            'credits_used': num_pages  # Example: 1 credit per page
        }
        
        # In the actual implementation, this would be saved to a database
        print(f"PDF generation tracked: {usage_info}")
        
        return usage_info


# Example usage
if __name__ == "__main__":
    generator = PDFGenerator()
    
    # Example single question and answer
    question = "What is the capital of France?"
    answer = "The capital of France is Paris. Paris is located in the north-central part of the country on the Seine River. It is the largest city in France and serves as the country's cultural, political, and economic center."
    
    single_output_path = "single_answer.pdf"
    result = generator.generate_single_answer_pdf(question, answer, single_output_path)
    
    if result:
        print(f"Single answer PDF generated: {result}")
        
        # Example of usage tracking
        usage_info = generator.track_pdf_generation("user123", single_output_path, 1)
    
    # Example bulk questions and answers
    bulk_questions = [
        {
            'question_number': '1',
            'section': 'A',
            'marks': '5',
            'question_text': 'Explain Newton\'s Third Law of Motion.',
            'answer': 'Newton\'s Third Law of Motion states that for every action, there is an equal and opposite reaction. This means that when one object exerts a force on a second object, the second object exerts an equal force in the opposite direction on the first object.',
            'confidence': 0.95,
            'sources': ['Physics Textbook', 'Newton\'s Principia']
        },
        {
            'question_number': '2',
            'section': 'A',
            'marks': '10',
            'question_text': 'Describe the process of photosynthesis.',
            'answer': 'Photosynthesis is the process by which green plants and some other organisms use sunlight to synthesize foods with carbon dioxide and water. It involves the conversion of light energy into chemical energy, which is then stored in the bonds of glucose. The process can be summarized by the equation: 6CO₂ + 6H₂O + light energy → C₆H₁₂O₆ + 6O₂.',
            'confidence': 0.92,
            'sources': ['Biology Textbook', 'Scientific Journal']
        },
        {
            'question_number': '1',
            'section': 'B',
            'marks': '15',
            'question_text': 'Analyze the causes and consequences of the Industrial Revolution.',
            'answer': 'The Industrial Revolution, which began in Britain in the late 18th century, was caused by a combination of factors including technological innovations, availability of capital, favorable economic conditions, and access to resources. Key technological developments included the steam engine, spinning jenny, and power loom. Consequences included urbanization, the rise of factory systems, improved living standards for many, but also pollution, poor working conditions, and social inequality. The revolution fundamentally transformed economic and social structures, leading to the modern capitalist economy.',
            'confidence': 0.88,
            'sources': ['History Textbook', 'Economic History Journal']
        }
    ]
    
    bulk_output_path = "bulk_answers.pdf"
    bulk_result = generator.generate_bulk_answers_pdf(bulk_questions, bulk_output_path)
    
    if bulk_result:
        print(f"Bulk answers PDF generated: {bulk_result}")
        
        # Example of usage tracking
        usage_info = generator.track_pdf_generation("user123", bulk_output_path, 3)
