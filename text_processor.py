"""
Text Processing Module for WhatsApp AI Agent

This module handles the processing of text-based questions received through WhatsApp.
It extracts questions, identifies intent, and prepares the text for the answer generation module.
"""

import re
import nltk
from nltk.tokenize import sent_tokenize
from typing import List, Dict, Any, Tuple

# Download necessary NLTK resources
try:
    nltk.data.find('tokenizers/punkt')
except LookupError:
    nltk.download('punkt')

class TextProcessor:
    """Class for processing text-based questions from WhatsApp messages."""
    
    def __init__(self):
        """Initialize the text processor."""
        self.question_patterns = [
            r'\b[Ww]hat\b.*\?',
            r'\b[Ww]ho\b.*\?',
            r'\b[Ww]hen\b.*\?',
            r'\b[Ww]here\b.*\?',
            r'\b[Ww]hy\b.*\?',
            r'\b[Hh]ow\b.*\?',
            r'\b[Cc]an\b.*\?',
            r'\b[Cc]ould\b.*\?',
            r'\b[Ss]hould\b.*\?',
            r'\b[Ww]ould\b.*\?',
            r'\b[Dd]o\b.*\?',
            r'\b[Dd]oes\b.*\?',
            r'\b[Ii]s\b.*\?',
            r'\b[Aa]re\b.*\?',
            r'\b[Ww]ill\b.*\?',
            r'.*\?'  # Catch-all for any sentence ending with a question mark
        ]
        
    def extract_questions(self, text: str) -> List[str]:
        """
        Extract questions from the input text.
        
        Args:
            text: Input text message
            
        Returns:
            List of extracted questions
        """
        # First, split text into sentences
        sentences = sent_tokenize(text)
        
        # Identify questions based on patterns and question marks
        questions = []
        for sentence in sentences:
            # Check if sentence ends with question mark
            if sentence.strip().endswith('?'):
                questions.append(sentence.strip())
                continue
                
            # Check if sentence matches question patterns
            for pattern in self.question_patterns:
                if re.search(pattern, sentence):
                    questions.append(sentence.strip())
                    break
        
        return questions
    
    def analyze_question(self, question: str) -> Dict[str, Any]:
        """
        Analyze a question to determine its type, domain, and key entities.
        
        Args:
            question: The question text
            
        Returns:
            Dictionary containing question analysis
        """
        # Simple question type classification
        question_type = self._classify_question_type(question)
        
        # Extract key entities (simplified implementation)
        entities = self._extract_entities(question)
        
        return {
            'question': question,
            'type': question_type,
            'entities': entities,
            'complexity': self._assess_complexity(question)
        }
    
    def _classify_question_type(self, question: str) -> str:
        """Classify the type of question."""
        question_lower = question.lower()
        
        if re.search(r'\b(what|define|describe)\b', question_lower):
            return 'factual'
        elif re.search(r'\b(how|process|steps)\b', question_lower):
            return 'procedural'
        elif re.search(r'\b(why|reason|cause)\b', question_lower):
            return 'explanatory'
        elif re.search(r'\b(compare|difference|versus|vs)\b', question_lower):
            return 'comparative'
        elif re.search(r'\b(evaluate|assess|analyze)\b', question_lower):
            return 'analytical'
        else:
            return 'general'
    
    def _extract_entities(self, question: str) -> List[str]:
        """Extract key entities from the question (simplified)."""
        # This is a simplified implementation
        # In a production system, you would use NER (Named Entity Recognition)
        words = question.split()
        # Remove common question words and stopwords
        stopwords = {'what', 'who', 'when', 'where', 'why', 'how', 'is', 'are', 'do', 'does', 
                    'can', 'could', 'should', 'would', 'the', 'a', 'an', 'in', 'on', 'at', 
                    'to', 'for', 'with', 'by', 'about', 'as', 'of', 'and', 'or'}
        
        entities = [word for word in words if word.lower() not in stopwords and len(word) > 2]
        return entities
    
    def _assess_complexity(self, question: str) -> str:
        """Assess the complexity of the question."""
        word_count = len(question.split())
        
        if word_count < 8:
            return 'simple'
        elif word_count < 15:
            return 'moderate'
        else:
            return 'complex'
    
    def process_text_input(self, text: str) -> List[Dict[str, Any]]:
        """
        Process text input to extract and analyze questions.
        
        Args:
            text: Input text message
            
        Returns:
            List of processed questions with analysis
        """
        questions = self.extract_questions(text)
        processed_questions = [self.analyze_question(q) for q in questions]
        
        return processed_questions


# Example usage
if __name__ == "__main__":
    processor = TextProcessor()
    
    # Example text with questions
    sample_text = """
    Hello, I have a few questions about physics. What is Newton's third law of motion? 
    Also, can you explain how quantum entanglement works? 
    I'm also curious about the difference between fission and fusion.
    """
    
    results = processor.process_text_input(sample_text)
    
    print(f"Extracted {len(results)} questions:")
    for i, result in enumerate(results, 1):
        print(f"\nQuestion {i}: {result['question']}")
        print(f"Type: {result['type']}")
        print(f"Complexity: {result['complexity']}")
        print(f"Key entities: {', '.join(result['entities'])}")
